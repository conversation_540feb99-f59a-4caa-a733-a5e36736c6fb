/**=================================================================**
* S:\IRMS.NET.2.5.2\irms.net\BE\BatchProcess\CycleCounts\CycleCounts_First.p : Bussiness Entity Proxy
*--------------------------------------------------------------------*
* Generated : 12/16/09, 14:31 PM
**=================================================================**/


/* Business Entity Definintions */
{BatchProcess/CycleCounts/CycleCounts_ds.i}
{BatchProcess/CycleCounts/CycleCounts_props.i}


/***************************************************************
* MAIN BLOCK 
***************************************************************/


    DEF INPUT        PARAM ipcContextID AS CHAR .
    DYNAMIC-FUNCTION('SetProperty' IN TARGET-PROCEDURE,'ContextID',ipcContextID) .

    DEF INPUT-OUTPUT PARAM DATASET FOR ds_Context .
    DEF       OUTPUT PARAM DATASET FOR dsCycleCounts .


    FIND FIRST ds_Control 
         WHERE ds_Control.PropName = 'COMMAND'
         NO-ERROR. 
    IF NOT AVAIL ds_Control THEN 
        CREATE ds_Control.
    ASSIGN ds_Control.PropName = 'COMMAND'
           ds_Control.PropValue = 'PREV'.


    RUN ProcessDataSet . 


/**************************** END OF FILE ****************************/


